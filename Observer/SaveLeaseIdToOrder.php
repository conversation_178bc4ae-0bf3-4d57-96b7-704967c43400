<?php
declare(strict_types=1);

namespace Webguru\ManualLease\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

class SaveLeaseIdToOrder implements ObserverInterface
{
    private const PAYMENT_METHOD_CODE = 'manuallease';

    /**
     * Save lease ID to order
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        $order = $observer->getEvent()->getOrder();
        $quote = $observer->getEvent()->getQuote();

        // Handle different event types
        if (!$order && $observer->getEvent()->getOrders()) {
            // For checkout_submit_all_after event
            $orders = $observer->getEvent()->getOrders();
            if (is_array($orders) && !empty($orders)) {
                $order = $orders[0];
            }
        }

        if (!$quote && $observer->getEvent()->getQuote()) {
            $quote = $observer->getEvent()->getQuote();
        }

        if (!$order || !$quote) {
            return;
        }

        $payment = $quote->getPayment();
        if ($payment && $payment->getMethod() === self::PAYMENT_METHOD_CODE) {
            $leaseId = $payment->getAdditionalInformation('lease_id');
            if ($leaseId && is_string($leaseId) && trim($leaseId) !== '') {
                $order->setData('manual_lease_id', trim($leaseId));
                // Force save the order to ensure the data is persisted
                try {
                    $order->save();
                } catch (\Exception $e) {
                    // Log error but don't break the flow
                    error_log('ManualLease: Failed to save lease ID to order: ' . $e->getMessage());
                }
            }
        }
    }
}
