<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="sales_model_service_quote_submit_before">
        <observer name="save_lease_id_to_order" instance="Webguru\ManualLease\Observer\SaveLeaseIdToOrder" />
    </event>

    <!-- Additional event to ensure lease ID is saved -->
    <event name="checkout_submit_all_after">
        <observer name="save_lease_id_to_order_after" instance="Webguru\ManualLease\Observer\SaveLeaseIdToOrder" />
    </event>

    <!-- Add lease ID to email template variables -->
    <event name="email_order_set_template_vars_before">
        <observer name="add_lease_id_to_email_template" instance="Webguru\ManualLease\Observer\AddLeaseIdToEmailTemplate" />
    </event>
</config>
