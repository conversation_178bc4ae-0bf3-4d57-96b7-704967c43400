# LeaseId Admin Display Troubleshooting Guide

## Issues Fixed

### 1. Fixed Malformed XML Configuration
- **File**: `etc/adminhtml/system.xml`
- **Issue**: Had `<s>` instead of `<system>` tag
- **Status**: ✅ FIXED

### 2. Improved Admin Grid Configuration
- **File**: `view/adminhtml/ui_component/sales_order_grid.xml`
- **Changes**: Added `dataType`, `visible`, and `translate` attributes
- **Status**: ✅ FIXED

### 3. Enhanced Observer Reliability
- **File**: `etc/events.xml` and `Observer/SaveLeaseIdToOrder.php`
- **Changes**: Added backup event and improved error handling
- **Status**: ✅ IMPROVED

## Steps to Resolve the Issue

### Step 1: Run the Debug Script
```bash
cd /path/to/magento/root
php debug_lease_id.php
```

This will check:
- Module enablement status
- Database column existence
- Existing orders with LeaseId
- Payment method configuration

### Step 2: Apply Database Schema
If the debug script shows the column is missing:
```bash
php bin/magento setup:db-schema:upgrade
```

### Step 3: Enable the Module
If the module is disabled:
```bash
php bin/magento module:enable Webguru_ManualLease
php bin/magento setup:upgrade
```

### Step 4: Clear Cache
```bash
php bin/magento cache:clean
php bin/magento cache:flush
```

### Step 5: Reindex (if needed)
```bash
php bin/magento indexer:reindex
```

### Step 6: Check Admin Configuration
1. Go to **Admin → Stores → Configuration → Sales → Payment Methods**
2. Find "Manual Lease" section
3. Ensure **Enabled** is set to "Yes"

## Verification Steps

### 1. Check Order Grid
- Go to **Admin → Sales → Orders**
- Look for "Lease ID" column
- If not visible, check column visibility settings

### 2. Check Order View
- Open any order that used Manual Lease payment
- Look for "Lease Information" section in payment details
- Should show the Lease ID if it was saved

### 3. Test New Order
1. Place a test order using Manual Lease payment method
2. Enter a test Lease ID during checkout
3. Check if it appears in admin after order completion

## Common Issues and Solutions

### Issue: Column not showing in grid
**Solution**: Clear admin cache and check UI component configuration

### Issue: LeaseId not saved to order
**Solution**: Check observer events and ensure module is properly enabled

### Issue: Payment method not available
**Solution**: Check payment method configuration and module enablement

### Issue: Database column missing
**Solution**: Run `setup:db-schema:upgrade` command

## Files Modified

1. `etc/adminhtml/system.xml` - Fixed malformed XML
2. `view/adminhtml/ui_component/sales_order_grid.xml` - Enhanced grid configuration
3. `etc/events.xml` - Added backup event observer
4. `Observer/SaveLeaseIdToOrder.php` - Improved error handling and reliability

## Debug Information

The debug script (`debug_lease_id.php`) provides comprehensive information about:
- Module status
- Database schema
- Existing orders with LeaseId
- Configuration settings
- Event manager status

Run this script first to identify the specific issue in your environment.
