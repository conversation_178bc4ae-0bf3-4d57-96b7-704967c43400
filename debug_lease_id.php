<?php
/**
 * Debug script to check LeaseId functionality
 * Run this from the Magento root directory: php debug_lease_id.php
 */

use Magento\Framework\App\Bootstrap;
use Magento\Framework\App\State;

require __DIR__ . '/app/bootstrap.php';

$bootstrap = Bootstrap::create(BP, $_SERVER);
$objectManager = $bootstrap->getObjectManager();

try {
    // Set area code
    $state = $objectManager->get(State::class);
    $state->setAreaCode('adminhtml');

    // Get resource connection
    $resource = $objectManager->get(\Magento\Framework\App\ResourceConnection::class);
    $connection = $resource->getConnection();

    echo "=== Manual Lease Debug Script ===\n\n";

    // 1. Check if module is enabled
    $moduleManager = $objectManager->get(\Magento\Framework\Module\Manager::class);
    $isEnabled = $moduleManager->isEnabled('Webguru_ManualLease');
    echo "1. Module Status: " . ($isEnabled ? "ENABLED" : "DISABLED") . "\n";

    // 2. Check if database column exists
    $tableName = $resource->getTableName('sales_order');
    $columns = $connection->describeTable($tableName);
    $columnExists = isset($columns['manual_lease_id']);
    echo "2. Database Column 'manual_lease_id': " . ($columnExists ? "EXISTS" : "MISSING") . "\n";

    if ($columnExists) {
        echo "   Column Details: " . json_encode($columns['manual_lease_id']) . "\n";
    }

    // 3. Check for orders with manual lease payment method
    $select = $connection->select()
        ->from(['so' => $tableName], ['entity_id', 'increment_id', 'manual_lease_id'])
        ->joinLeft(
            ['sop' => $resource->getTableName('sales_order_payment')],
            'so.entity_id = sop.parent_id',
            ['method']
        )
        ->where('sop.method = ?', 'manuallease')
        ->limit(10);

    $orders = $connection->fetchAll($select);
    echo "3. Orders with Manual Lease Payment: " . count($orders) . " found\n";

    if (!empty($orders)) {
        echo "   Recent orders:\n";
        foreach ($orders as $order) {
            echo "   - Order #{$order['increment_id']} (ID: {$order['entity_id']}) - Lease ID: " . 
                 ($order['manual_lease_id'] ?: 'NULL') . "\n";
        }
    }

    // 4. Check payment configuration
    $scopeConfig = $objectManager->get(\Magento\Framework\App\Config\ScopeConfigInterface::class);
    $isActive = $scopeConfig->getValue('payment/manuallease/active');
    $title = $scopeConfig->getValue('payment/manuallease/title');
    echo "4. Payment Method Configuration:\n";
    echo "   - Active: " . ($isActive ? "YES" : "NO") . "\n";
    echo "   - Title: " . ($title ?: 'Not Set') . "\n";

    // 5. Check if events are properly configured
    $eventManager = $objectManager->get(\Magento\Framework\Event\ManagerInterface::class);
    echo "5. Event Manager: " . (get_class($eventManager)) . "\n";

    echo "\n=== Debug Complete ===\n";

} catch (\Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
